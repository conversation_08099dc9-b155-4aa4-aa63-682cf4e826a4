import json
import pathlib
import time
import os

import requests


def execute_workflow(image_url, input_prompt, weight, cloudrun_url="https://comfyui-cloudrun-435103809426.asia-southeast1.run.app"):
    workflow_path = pathlib.Path(__file__).parent / "workflow.json"
    output_node = input("Output node: ")
    
    
    with open(workflow_path) as f:
        workflow = json.load(f)
    workflow["22"]["inputs"]["text"] = input_prompt + " blur street background, detailed skin, realistic skin texture, dramatic, cinematic, dof, 8k uhd, dslr, high quality"
    workflow["12"]["inputs"]["image"] = image_url
    workflow["33"]["inputs"]["end_at"] = weight
    workflow["57"]["inputs"]["weight"] = weight
    print(f'prompt: {workflow["22"]["inputs"]["text"]} , image: {workflow["12"]["inputs"]["image"]}')
    prompt = {"prompt": workflow}

    response = requests.post(f"{cloudrun_url}/prompt", json=prompt)
    response.raise_for_status()
    print("Response:")
    print(response.text)

    output = response.json().get("outputs", {}).get(output_node, None)
    print(f"Output for node '{output_node}': {output}")

    filename = output["images"][0]["filename"]
    output_url = f"{cloudrun_url}/view?filename={filename}"
    print(f"Output URL: {output_url}")
    return {"output_url": output_url}
