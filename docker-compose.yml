version: '3.8'

services:
  image-upload-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: image-upload-api
    ports:
      - "8000:8000"
    environment:
      - PYTHONUNBUFFERED=1
      - GOOGLE_APPLICATION_CREDENTIALS=/app/gcp-credentials.json
    volumes:
      # Mount the credentials file
      - ./gcp-credentials.json:/app/gcp-credentials.json:ro
      # Optional: Mount logs directory for persistent logging
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - image-upload-network

  # Optional: Nginx reverse proxy for production
  nginx:
    image: nginx:alpine
    container_name: image-upload-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro  # For SSL certificates if needed
    depends_on:
      - image-upload-api
    restart: unless-stopped
    networks:
      - image-upload-network
    profiles:
      - production  # Only start with --profile production

networks:
  image-upload-network:
    driver: bridge

volumes:
  logs:
    driver: local
