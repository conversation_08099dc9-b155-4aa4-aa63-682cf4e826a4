# Environment variables for Image Upload API

# Google Cloud Storage Configuration
BUCKET_NAME=comfyui-data-analytic-project-424703
FOLDER_PATH=comfyui/input
GOOGLE_APPLICATION_CREDENTIALS=/app/gcp-credentials.json

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
MAX_FILE_SIZE_MB=10

# Logging
LOG_LEVEL=INFO
PYTHONUNBUFFERED=1

# Environment
ENVIRONMENT=production

# CORS Settings (comma-separated origins)
ALLOWED_ORIGINS=*

# Rate Limiting (requests per second)
UPLOAD_RATE_LIMIT=2
API_RATE_LIMIT=10
