import os
import logging
from typing import Optional
from fastapi import Fast<PERSON><PERSON>, File, UploadFile, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import magic
from PIL import Image
import io

from gcp_storage import GCPStorageClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Image Upload API",
    description="API for uploading images to Google Cloud Storage",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
BUCKET_NAME = "comfyui-data-analytic-project-424703"
FOLDER_PATH = "comfyui/input"
CREDENTIALS_PATH = "gcp-credentials.json"

# Supported image formats
SUPPORTED_IMAGE_TYPES = {
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'image/gif': ['.gif'],
    'image/webp': ['.webp'],
    'image/bmp': ['.bmp'],
    'image/tiff': ['.tiff', '.tif']
}

# Maximum file size (20MB)
MAX_FILE_SIZE = 20 * 1024 * 1024

# Initialize GCP Storage client
try:
    storage_client = GCPStorageClient(
        bucket_name=BUCKET_NAME,
        credentials_path=CREDENTIALS_PATH
    )
    logger.info("GCP Storage client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize GCP Storage client: {str(e)}")
    storage_client = None


def validate_image_file(file: UploadFile) -> tuple[bool, str]:
    """
    Validate uploaded file is a valid image.
    
    Args:
        file: Uploaded file object
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        # Check file size
        file.file.seek(0, 2)  # Seek to end
        file_size = file.file.tell()
        file.file.seek(0)  # Reset to beginning
        
        if file_size > MAX_FILE_SIZE:
            return False, f"File size ({file_size} bytes) exceeds maximum allowed size ({MAX_FILE_SIZE} bytes)"
        
        if file_size == 0:
            return False, "File is empty"
        
        # Check content type
        if file.content_type not in SUPPORTED_IMAGE_TYPES:
            return False, f"Unsupported file type: {file.content_type}. Supported types: {list(SUPPORTED_IMAGE_TYPES.keys())}"
        
        # Validate file content by trying to open it as an image
        file_content = file.file.read()
        file.file.seek(0)  # Reset for later use
        
        try:
            image = Image.open(io.BytesIO(file_content))
            image.verify()  # Verify it's a valid image
            logger.info(f"Image validation successful: {image.format}, {image.size}")
        except Exception as e:
            return False, f"Invalid image file: {str(e)}"
        
        # Additional MIME type validation using python-magic
        try:
            mime_type = magic.from_buffer(file_content, mime=True)
            if mime_type not in SUPPORTED_IMAGE_TYPES:
                return False, f"File content doesn't match expected image type. Detected: {mime_type}"
        except Exception as e:
            logger.warning(f"Could not validate MIME type with python-magic: {str(e)}")
        
        return True, ""
        
    except Exception as e:
        return False, f"Error validating file: {str(e)}"


@app.get("/")
async def root():
    """Health check endpoint."""
    return {
        "message": "Image Upload API is running",
        "bucket": BUCKET_NAME,
        "folder": FOLDER_PATH,
        "supported_formats": list(SUPPORTED_IMAGE_TYPES.keys()),
        "max_file_size_mb": MAX_FILE_SIZE / (1024 * 1024)
    }


@app.get("/health")
async def health_check():
    """Detailed health check including GCP connectivity."""
    health_status = {
        "status": "healthy",
        "gcp_storage": "connected" if storage_client else "disconnected",
        "bucket": BUCKET_NAME,
        "folder": FOLDER_PATH
    }
    
    if not storage_client:
        health_status["status"] = "unhealthy"
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content=health_status
        )
    
    return health_status


@app.post("/upload-image")
async def upload_image(file: UploadFile = File(...)):
    """
    Upload an image file to Google Cloud Storage.
    
    Args:
        file: Image file to upload
        
    Returns:
        JSON response with upload status and public URL
    """
    try:
        # Check if storage client is available
        if not storage_client:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="GCP Storage service is not available"
            )
        
        # Validate the uploaded file
        is_valid, error_message = validate_image_file(file)
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_message
            )
        
        # Generate unique filename with folder path
        unique_filename = storage_client.generate_unique_filename(
            original_filename=file.filename,
            folder_path=FOLDER_PATH
        )
        
        logger.info(f"Uploading file: {file.filename} -> {unique_filename}")
        
        # Upload file to GCS
        public_url = storage_client.upload_file_from_memory(
            file_obj=file.file,
            destination_blob_name=unique_filename,
            content_type=file.content_type,
            make_public=True
        )
        
        if not public_url:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to upload file to Google Cloud Storage"
            )
        
        # Get file info
        file.file.seek(0, 2)
        file_size = file.file.tell()
        
        response_data = {
            "success": True,
            "message": "Image uploaded successfully",
            "data": {
                "original_filename": file.filename,
                "uploaded_filename": unique_filename,
                "public_url": public_url,
                "content_type": file.content_type,
                "file_size_bytes": file_size,
                "bucket": BUCKET_NAME,
                "folder": FOLDER_PATH
            }
        }
        
        logger.info(f"Successfully uploaded {file.filename} to {public_url}")
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during file upload: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@app.delete("/delete-image/{filename}")
async def delete_image(filename: str):
    """
    Delete an image from Google Cloud Storage.
    
    Args:
        filename: Name of the file to delete (including folder path)
        
    Returns:
        JSON response with deletion status
    """
    try:
        # Check if storage client is available
        if not storage_client:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="GCP Storage service is not available"
            )
        
        # Ensure filename includes the folder path
        if not filename.startswith(FOLDER_PATH):
            filename = f"{FOLDER_PATH}/{filename}"
        
        logger.info(f"Deleting file: {filename}")
        
        # Delete file from GCS
        success = storage_client.delete_file(filename)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File not found or could not be deleted: {filename}"
            )
        
        response_data = {
            "success": True,
            "message": "Image deleted successfully",
            "data": {
                "deleted_filename": filename,
                "bucket": BUCKET_NAME
            }
        }
        
        logger.info(f"Successfully deleted {filename}")
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during file deletion: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
