#!/bin/bash

# Docker startup script for Image Upload API

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    print_error "docker-compose is not installed. Please install docker-compose and try again."
    exit 1
fi

# Check if credentials file exists
if [ ! -f "gcp-credentials.json" ]; then
    print_error "gcp-credentials.json file not found!"
    print_error "Please ensure your Google Cloud service account credentials are in this file."
    exit 1
fi

print_status "Starting Image Upload API with Docker..."

# Parse command line arguments
ENVIRONMENT="production"
PROFILE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --dev|--development)
            ENVIRONMENT="development"
            shift
            ;;
        --prod|--production)
            ENVIRONMENT="production"
            PROFILE="--profile production"
            shift
            ;;
        --build)
            BUILD_FLAG="--build"
            shift
            ;;
        --down)
            print_status "Stopping and removing containers..."
            docker-compose down
            if [ "$ENVIRONMENT" = "development" ]; then
                docker-compose -f docker-compose.dev.yml down
            fi
            print_success "Containers stopped and removed."
            exit 0
            ;;
        --logs)
            print_status "Showing logs..."
            if [ "$ENVIRONMENT" = "development" ]; then
                docker-compose -f docker-compose.dev.yml logs -f
            else
                docker-compose logs -f $PROFILE
            fi
            exit 0
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --dev, --development    Run in development mode with hot reload"
            echo "  --prod, --production    Run in production mode with Nginx"
            echo "  --build                 Force rebuild of Docker images"
            echo "  --down                  Stop and remove containers"
            echo "  --logs                  Show container logs"
            echo "  --help, -h              Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                      # Run in production mode"
            echo "  $0 --dev               # Run in development mode"
            echo "  $0 --prod --build      # Run in production mode and rebuild images"
            echo "  $0 --down              # Stop all containers"
            echo "  $0 --logs              # Show logs"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            print_error "Use --help for usage information."
            exit 1
            ;;
    esac
done

# Create logs directory if it doesn't exist
mkdir -p logs

print_status "Environment: $ENVIRONMENT"

if [ "$ENVIRONMENT" = "development" ]; then
    print_status "Starting development environment..."
    print_warning "Development mode includes hot reload - code changes will restart the server"
    
    docker-compose -f docker-compose.dev.yml up -d $BUILD_FLAG
    
    print_success "Development environment started!"
    print_status "API available at: http://localhost:8000"
    print_status "API docs available at: http://localhost:8000/docs"
    print_status "Health check: http://localhost:8000/health"
    
else
    print_status "Starting production environment..."
    
    docker-compose up -d $BUILD_FLAG $PROFILE
    
    print_success "Production environment started!"
    if [[ "$PROFILE" == *"production"* ]]; then
        print_status "API available at: http://localhost (via Nginx)"
        print_status "Direct API access: http://localhost:8000"
    else
        print_status "API available at: http://localhost:8000"
    fi
    print_status "API docs available at: http://localhost:8000/docs"
    print_status "Health check: http://localhost:8000/health"
fi

print_status "Container status:"
docker-compose ps

print_status ""
print_status "Useful commands:"
print_status "  View logs:           $0 --logs"
print_status "  Stop containers:     $0 --down"
print_status "  Rebuild and start:   $0 --build"

print_status ""
print_status "To test the API, you can:"
print_status "1. Open test_upload.html in your browser"
print_status "2. Use curl: curl -X POST 'http://localhost:8000/upload-image' -F 'file=@image.jpg'"
print_status "3. Visit the interactive docs: http://localhost:8000/docs"
