version: '3.8'

services:
  image-upload-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: image-upload-api
    ports:
      - "8000:8000"
    environment:
      - PYTHONUNBUFFERED=1
      - GOOGLE_APPLICATION_CREDENTIALS=/app/gcp-credentials.json
    volumes:
      - ./gcp-credentials.json:/app/gcp-credentials.json:ro
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
