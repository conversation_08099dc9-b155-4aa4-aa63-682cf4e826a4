import os
import logging
from typing import Optional, BinaryIO
from google.cloud import storage
from google.oauth2 import service_account
import uuid
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class GCPStorageClient:
    """Client for interacting with Google Cloud Storage."""

    def __init__(self, bucket_name: str, credentials_path: Optional[str] = None):
        """
        Initialize the GCP Storage client.

        Args:
            bucket_name: Name of the GCS bucket to use
            credentials_path: Path to the service account credentials JSON file.
                              If None, will use the GOOGLE_APPLICATION_CREDENTIALS env var.
        """
        self.bucket_name = bucket_name

        logger.info(f"Initializing GCPStorageClient with bucket_name={bucket_name}")
        logger.info(f"Explicit credentials_path={credentials_path}")
        logger.info(f"GOOGLE_APPLICATION_CREDENTIALS env var={os.environ.get('GOOGLE_APPLICATION_CREDENTIALS', 'Not set')}")

        try:
            # Initialize the storage client
            if credentials_path and os.path.exists(credentials_path):
                # Use explicit credentials file
                logger.info(f"Using explicit credentials file: {credentials_path}")
                credentials = service_account.Credentials.from_service_account_file(credentials_path)
                self.client = storage.Client(credentials=credentials)
                logger.info(f"Successfully initialized GCP Storage client with credentials from {credentials_path}")
            else:
                # Use default credentials (GOOGLE_APPLICATION_CREDENTIALS env var)
                logger.info("Using default credentials from GOOGLE_APPLICATION_CREDENTIALS env var")
                self.client = storage.Client()
                logger.info("Successfully initialized GCP Storage client with default credentials")

            # Get the bucket
            logger.info(f"Getting bucket: {bucket_name}")
            self.bucket = self.client.bucket(bucket_name)

            # Check if bucket exists, if not create it
            logger.info(f"Checking if bucket {bucket_name} exists")
            try:
                bucket_exists = self.bucket.exists()
                if not bucket_exists:
                    logger.warning(f"Bucket {bucket_name} does not exist. Creating it...")
                    self.bucket = self.client.create_bucket(bucket_name)
                    logger.info(f"Successfully created bucket {bucket_name}")
                else:
                    logger.info(f"Using existing bucket {bucket_name}")
            except Exception as e:
                # Handle permission error gracefully
                logger.warning(f"Unable to check if bucket {bucket_name} exists due to permissions: {str(e)}")
                logger.info(f"Assuming bucket {bucket_name} exists and continuing")

            # Log project info
            logger.info(f"GCP Project ID: {self.client.project}")

        except Exception as e:
            logger.error(f"Error initializing GCP Storage client: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise

    def upload_file(self, source_file_path: str, destination_blob_name: str,
                    content_type: str = 'application/pdf', make_public: bool = True) -> Optional[str]:
        """
        Upload a file to GCP Storage.

        Args:
            source_file_path: Path to the local file to upload
            destination_blob_name: Name to give the file in GCS
            content_type: MIME type of the file
            make_public: Whether to make the file publicly accessible

        Returns:
            Public URL of the uploaded file if make_public=True, otherwise None
        """
        try:
            logger.info(f"GCPStorageClient.upload_file called with: source={source_file_path}, dest={destination_blob_name}")

            # Check if credentials are properly loaded
            if hasattr(self.client, '_credentials'):
                logger.info(f"GCP credentials loaded: {self.client._credentials is not None}")
                if hasattr(self.client._credentials, 'service_account_email'):
                    logger.info(f"Using service account: {self.client._credentials.service_account_email}")
            else:
                logger.warning("GCP client does not have _credentials attribute")

            # Verify file exists and is readable
            if not os.path.exists(source_file_path):
                logger.error(f"Source file {source_file_path} does not exist")
                return None

            if not os.access(source_file_path, os.R_OK):
                logger.error(f"Source file {source_file_path} is not readable")
                return None

            # Log file details
            file_size = os.path.getsize(source_file_path)
            logger.info(f"File size: {file_size} bytes, Content type: {content_type}")

            # Verify bucket exists
            logger.info(f"Checking if bucket {self.bucket_name} exists")
            try:
                bucket_exists = self.bucket.exists()
                if not bucket_exists:
                    logger.error(f"Bucket {self.bucket_name} does not exist")
                    return None
            except Exception as e:
                # Handle permission error gracefully
                logger.warning(f"Unable to check if bucket {self.bucket_name} exists due to permissions: {str(e)}")
                logger.info(f"Assuming bucket {self.bucket_name} exists and continuing")

            # Create a blob object
            logger.info(f"Creating blob object for {destination_blob_name}")
            blob = self.bucket.blob(destination_blob_name)

            # Upload the file
            logger.info(f"Starting upload from {source_file_path} to GCS")
            blob.upload_from_filename(source_file_path, content_type=content_type)
            logger.info(f"Successfully uploaded {source_file_path} to {destination_blob_name} in bucket {self.bucket_name}")

            # Generate the public URL regardless of whether we can make it public
            # This works because the bucket might have public access at the bucket level
            public_url = blob.public_url

            # Try to make the blob publicly accessible if requested
            if make_public:
                try:
                    logger.info("Attempting to make blob publicly accessible")
                    blob.make_public()
                    logger.info(f"File is publicly accessible at {public_url}")
                except Exception as e:
                    # This will happen if uniform bucket-level access is enabled
                    logger.warning(f"Could not set individual ACL on blob: {str(e)}")
                    logger.info("Using public URL based on bucket-level permissions")

                logger.info(f"Public URL: {public_url}")
                return public_url
            else:
                logger.info("Blob is not set to be publicly accessible")

            return None

        except Exception as e:
            logger.error(f"Error uploading file to GCP Storage: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    def upload_file_from_memory(self, file_obj: BinaryIO, destination_blob_name: str,
                               content_type: str = 'image/jpeg', make_public: bool = True) -> Optional[str]:
        """
        Upload a file from memory (file-like object) to GCP Storage.

        Args:
            file_obj: File-like object containing the file data
            destination_blob_name: Name to give the file in GCS
            content_type: MIME type of the file
            make_public: Whether to make the file publicly accessible

        Returns:
            Public URL of the uploaded file if make_public=True, otherwise None
        """
        try:
            logger.info(f"GCPStorageClient.upload_file_from_memory called with dest={destination_blob_name}")

            # Check if credentials are properly loaded
            if hasattr(self.client, '_credentials'):
                logger.info(f"GCP credentials loaded: {self.client._credentials is not None}")
                if hasattr(self.client._credentials, 'service_account_email'):
                    logger.info(f"Using service account: {self.client._credentials.service_account_email}")
            else:
                logger.warning("GCP client does not have _credentials attribute")

            # Log file details
            file_obj.seek(0, 2)  # Seek to end to get size
            file_size = file_obj.tell()
            file_obj.seek(0)  # Reset to beginning
            logger.info(f"File size: {file_size} bytes, Content type: {content_type}")

            # Verify bucket exists
            logger.info(f"Checking if bucket {self.bucket_name} exists")
            try:
                bucket_exists = self.bucket.exists()
                if not bucket_exists:
                    logger.error(f"Bucket {self.bucket_name} does not exist")
                    return None
            except Exception as e:
                # Handle permission error gracefully
                logger.warning(f"Unable to check if bucket {self.bucket_name} exists due to permissions: {str(e)}")
                logger.info(f"Assuming bucket {self.bucket_name} exists and continuing")

            # Create a blob object
            logger.info(f"Creating blob object for {destination_blob_name}")
            blob = self.bucket.blob(destination_blob_name)

            # Upload the file from memory
            logger.info(f"Starting upload from memory to GCS")
            blob.upload_from_file(file_obj, content_type=content_type)
            logger.info(f"Successfully uploaded file to {destination_blob_name} in bucket {self.bucket_name}")

            # Generate the public URL regardless of whether we can make it public
            # This works because the bucket might have public access at the bucket level
            public_url = blob.public_url

            # Try to make the blob publicly accessible if requested
            if make_public:
                try:
                    logger.info("Attempting to make blob publicly accessible")
                    blob.make_public()
                    logger.info(f"File is publicly accessible at {public_url}")
                except Exception as e:
                    # This will happen if uniform bucket-level access is enabled
                    logger.warning(f"Could not set individual ACL on blob: {str(e)}")
                    logger.info("Using public URL based on bucket-level permissions")

                logger.info(f"Public URL: {public_url}")
                return public_url
            else:
                logger.info("Blob is not set to be publicly accessible")

            return None

        except Exception as e:
            logger.error(f"Error uploading file to GCP Storage: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    def generate_unique_filename(self, original_filename: str, folder_path: str = "") -> str:
        """
        Generate a unique filename with timestamp and UUID.

        Args:
            original_filename: Original filename with extension
            folder_path: Optional folder path to prepend

        Returns:
            Unique filename with folder path
        """
        # Extract file extension
        name, ext = os.path.splitext(original_filename)

        # Generate timestamp and UUID
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]

        # Create unique filename
        unique_filename = f"{timestamp}_{unique_id}{ext}"

        # Add folder path if provided
        if folder_path:
            # Ensure folder path ends with /
            if not folder_path.endswith('/'):
                folder_path += '/'
            unique_filename = folder_path + unique_filename

        return unique_filename

    def delete_file(self, blob_name: str) -> bool:
        """
        Delete a file from GCP Storage.

        Args:
            blob_name: Name of the blob to delete

        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            blob = self.bucket.blob(blob_name)
            blob.delete()
            logger.info(f"Deleted {blob_name} from bucket {self.bucket_name}")
            return True
        except Exception as e:
            logger.error(f"Error deleting file from GCP Storage: {str(e)}")
            return False
