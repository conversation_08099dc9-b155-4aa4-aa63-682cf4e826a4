#!/usr/bin/env python3
"""
Simple script to run the FastAPI image upload server.
"""

import uvicorn
import os
import sys

def main():
    """Run the FastAPI server."""
    
    # Check if credentials file exists
    credentials_path = "gcp-credentials.json"
    if not os.path.exists(credentials_path):
        print(f"❌ Error: GCP credentials file '{credentials_path}' not found!")
        print("Please make sure your Google Cloud service account credentials are in this file.")
        sys.exit(1)
    
    print("🚀 Starting FastAPI Image Upload Server...")
    print("📁 Bucket: comfyui-data-analytic-project-424703")
    print("📂 Folder: comfyui/input")
    print("🌐 Server will be available at: http://localhost:8000")
    print("🧪 Test page will be available at: test_upload.html")
    print("📚 API docs will be available at: http://localhost:8000/docs")
    print("\n" + "="*50)
    
    try:
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
