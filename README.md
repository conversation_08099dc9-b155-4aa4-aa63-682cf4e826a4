# Image Upload API for Google Cloud Storage

A FastAPI-based web service for uploading images to Google Cloud Storage bucket `comfyui-data-analytic-project-424703` in the `comfyui/input` folder.

## Features

- ✅ Upload images to Google Cloud Storage
- ✅ Automatic image validation (format, size, content)
- ✅ Unique filename generation with timestamps
- ✅ Public URL generation for uploaded images
- ✅ Support for multiple image formats (JPEG, PNG, GIF, WebP, BMP, TIFF)
- ✅ File size validation (max 10MB)
- ✅ CORS enabled for web frontend integration
- ✅ Comprehensive error handling and logging
- ✅ Health check endpoints
- ✅ Interactive API documentation
- ✅ Test HTML page included

## Setup

### Option 1: Docker (Recommended)

1. **Ensure Docker is installed and running**

2. **Make sure your GCP credentials are in place**:
   ```bash
   # Your gcp-credentials.json should be in the project root
   ls gcp-credentials.json
   ```

3. **Start the application**:
   ```bash
   # Build and start the container
   docker-compose up -d --build

   # View logs
   docker-compose logs -f

   # Stop the container
   docker-compose down
   ```

### Option 2: Local Python Setup

#### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

#### 2. Google Cloud Setup

Make sure your `gcp-credentials.json` file is in the project root with the correct service account credentials that have access to the bucket `comfyui-data-analytic-project-424703`.

#### 3. Run the Server

```bash
python run_server.py
```

Or directly with uvicorn:

```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## API Endpoints

### Upload Image
- **POST** `/upload-image`
- Upload an image file to Google Cloud Storage
- **Body**: Form data with `file` field containing the image
- **Response**: JSON with upload status and public URL

### Health Check
- **GET** `/health`
- Check API and GCP connectivity status

### Root
- **GET** `/`
- Basic API information and configuration

### Delete Image
- **DELETE** `/delete-image/{filename}`
- Delete an image from Google Cloud Storage

## Usage Examples

### Using curl

```bash
# Upload an image
curl -X POST "http://localhost:8000/upload-image" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@your_image.jpg"

# Health check
curl -X GET "http://localhost:8000/health"
```

### Using the Test Page

1. Start the server: `python run_server.py`
2. Open `test_upload.html` in your web browser
3. Select or drag & drop an image file
4. Click "Upload Image"
5. View the result with the public URL

### Using Python requests

```python
import requests

# Upload image
with open('your_image.jpg', 'rb') as f:
    files = {'file': f}
    response = requests.post('http://localhost:8000/upload-image', files=files)
    result = response.json()
    print(f"Public URL: {result['data']['public_url']}")
```

## Configuration

The following settings can be modified in `main.py`:

- `BUCKET_NAME`: Google Cloud Storage bucket name
- `FOLDER_PATH`: Folder path within the bucket
- `CREDENTIALS_PATH`: Path to GCP service account credentials
- `MAX_FILE_SIZE`: Maximum allowed file size (default: 10MB)
- `SUPPORTED_IMAGE_TYPES`: Supported image MIME types

## Docker Configuration

### Features

- **Health checks** for container monitoring
- **Volume mounts** for persistent logs and credentials
- **Automatic restart** on failure
- **Simple single-service setup**

## File Structure

```
.
├── main.py                 # FastAPI application
├── gcp_storage.py         # Google Cloud Storage client
├── gcp-credentials.json   # GCP service account credentials
├── requirements.txt       # Python dependencies
├── run_server.py         # Server startup script
├── test_upload.html      # Test web page
├── Dockerfile            # Docker image definition
├── docker-compose.yml    # Docker setup
├── .dockerignore         # Docker ignore file
└── README.md             # This file
```

## API Documentation

Once the server is running, visit:
- **Interactive docs**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## Error Handling

The API includes comprehensive error handling for:
- Invalid file formats
- File size limits
- GCP connectivity issues
- Authentication problems
- Network errors

## Security Notes

- The current CORS configuration allows all origins (`*`) for development
- In production, update the CORS settings to only allow specific domains
- Ensure your GCP service account has minimal required permissions
- Consider implementing rate limiting for production use

## Troubleshooting

### Common Issues

1. **"GCP Storage service is not available"**
   - Check that `gcp-credentials.json` exists and is valid
   - Verify the service account has access to the bucket

2. **"Bucket does not exist"**
   - Ensure the bucket name is correct
   - Check that the service account has access to the bucket

3. **"Could not set individual ACL on blob"**
   - This is normal if uniform bucket-level access is enabled
   - The file will still be publicly accessible if the bucket is configured for public access

4. **File upload fails**
   - Check file size (must be ≤ 10MB)
   - Verify file format is supported
   - Check network connectivity to Google Cloud

## License

This project is provided as-is for educational and development purposes.
